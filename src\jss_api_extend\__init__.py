# -*- coding: utf-8 -*-
"""
JSS API Extension Package

A comprehensive Python package for social media platform APIs (Douyin, Xiaohongshu, Kuaishou) 
and DingTalk Bitable integration.

This package provides:
- adapter: API adapters for social media platforms (Douyin, Xiaohongshu, Kuaishou)
- bitable: DingTalk Bitable integration and notification services
- client: Internal API clients for third-party services
- common: Common utilities and configurations
- utils: Utility functions and helpers
"""

# 版本信息从 pyproject.toml 中动态获取
try:
    from importlib.metadata import version, PackageNotFoundError
except ImportError:
    # Python < 3.8
    from importlib_metadata import version, PackageNotFoundError

try:
    __version__ = version("jss-api-extend")
except PackageNotFoundError:
    # 开发模式下的后备版本
    __version__ = "0.1.0-dev"
__author__ = "JSS Team"
__email__ = "<EMAIL>"

# Import main classes for easy access
from .adapter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>hs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    TiktokSearchHandler,
)

from .bitable import (
    DingTalkBitableNotifier,
    TaoTianDingTalkBitableNotifier,
    DingtalkBitableFactory,
    DingTalkAlerter,
)

from .client import (
    TikhubDouyinClient,
    TikhubXhsClient,
    TikhubKuaiShouClient,
    LarkBitable,
    ServiceClient,
    DingTalkLinker,
)

# Export all main classes
__all__ = [
    # Adapter classes
    "DouyinDetailHandler",
    "DouyinSearchHandler", 
    "KuaiShouDetailHandler",
    "XhsDetailHandler",
    "XhsCommentHandler",
    "TiktokSearchHandler",
    
    # Bitable classes
    "DingTalkBitableNotifier",
    "TaoTianDingTalkBitableNotifier",
    "DingtalkBitableFactory",
    "DingTalkAlerter",

    "ServiceClient",
]
